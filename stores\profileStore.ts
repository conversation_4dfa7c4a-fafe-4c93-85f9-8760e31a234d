import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {
  UserProfile,
  ProfileSettings,
  ProfilePhoto,
  ProfileValidation,
  ProfileUpdateRequest,
  PhotoUploadRequest,
  ProfileAnalytics,
  PROFILE_COMPLETION_WEIGHTS
} from '@/types/profile';
import { usePremiumStore } from '@/stores/premiumStore';
import { settingsSyncService } from '../services/settingsSync';
// Note: profileService will be imported when created
import * as Haptics from 'expo-haptics';
import { Platform } from 'react-native';

interface ProfileState {
  // Profile data
  profile: UserProfile | null;
  settings: ProfileSettings | null;
  analytics: ProfileAnalytics | null;
  
  // UI state
  isLoading: boolean;
  isUpdating: boolean;
  isUploadingPhoto: boolean;
  error: string | null;
  
  // Validation
  validation: ProfileValidation | null;
  
  // Actions
  loadProfile: () => Promise<void>;
  updateProfile: (updates: Partial<UserProfile>) => Promise<void>;
  updateSettings: (settings: Partial<ProfileSettings>) => Promise<void>;
  resetSettings: () => Promise<void>;
  exportSettings: () => Promise<string>;
  importSettings: (jsonData: string) => Promise<void>;
  createSettingsBackup: () => Promise<void>;
  restoreSettingsBackup: () => Promise<void>;
  getSettingsHistory: () => Promise<Array<{ settings: ProfileSettings; source: string; timestamp: Date }>>;

  // Photo management
  uploadPhoto: (photo: PhotoUploadRequest) => Promise<string>;
  deletePhoto: (photoId: string) => Promise<void>;
  reorderPhotos: (photoIds: string[]) => Promise<void>;
  setMainPhoto: (photoId: string) => Promise<void>;
  editPhoto: (photoId: string, editedUri: string) => Promise<void>;
  verifyPhoto: (photoId: string) => Promise<void>;
  
  // Profile validation
  validateProfile: (profile?: Partial<UserProfile>) => ProfileValidation;
  calculateCompletion: (profile?: UserProfile) => number;
  
  // Analytics
  loadAnalytics: () => Promise<void>;
  trackProfileView: () => void;

  // Premium integration
  updatePremiumStatus: () => void;
  getPremiumBadgeInfo: () => { isPremium: boolean; badgeText?: string };

  // Utility
  clearError: () => void;
  reset: () => void;
}

const initialState = {
  profile: null,
  settings: null,
  analytics: null,
  isLoading: false,
  isUpdating: false,
  isUploadingPhoto: false,
  error: null,
  validation: null,
};

// Default profile for demo purposes
const createDefaultProfile = (): UserProfile => ({
  id: 'current-user',
  email: '<EMAIL>',
  firstName: 'Alex',
  lastName: 'Johnson',
  name: 'Alex',
  age: 28,
  dateOfBirth: new Date('1995-06-15'),
  bio: 'Adventure seeker, coffee enthusiast, and weekend hiker. Looking for someone to explore the city with! 🌟',
  photos: [
    {
      id: 'photo-1',
      url: 'https://images.pexels.com/photos/1130626/pexels-photo-1130626.jpeg?auto=compress&cs=tinysrgb&w=400',
      order: 0,
      isMain: true,
      uploadedAt: new Date(),
    },
    {
      id: 'photo-2',
      url: 'https://images.pexels.com/photos/1542085/pexels-photo-1542085.jpeg?auto=compress&cs=tinysrgb&w=400',
      order: 1,
      isMain: false,
      uploadedAt: new Date(),
    },
  ],
  location: {
    city: 'New York',
    state: 'NY',
    country: 'USA',
    coordinates: {
      latitude: 40.7128,
      longitude: -74.0060,
    },
  },
  occupation: 'Marketing Manager',
  education: 'Columbia University',
  height: 175,
  interests: ['Travel', 'Coffee', 'Hiking', 'Photography'],
  languages: ['English', 'Spanish'],
  relationshipGoals: 'serious',
  verified: true,
  isPremium: false,
  isOnline: true,
  lastSeen: new Date(),
  profileCompletion: 85,
  verificationStatus: {
    identity: {
      verified: false,
      status: 'not_started',
    },
    phone: {
      verified: false,
      status: 'not_started',
    },
    email: {
      verified: true,
      verifiedAt: new Date(),
      status: 'approved',
    },
    photo: {
      verified: false,
      status: 'not_started',
    },
  },
  blockedUsers: [],
  reportedUsers: [],
  socialMedia: {
    instagram: '@alex_adventures',
    spotify: 'alex.music',
  },
  createdAt: new Date('2023-01-15'),
  updatedAt: new Date(),
});

// Default settings
const createDefaultSettings = (): ProfileSettings => ({
  dating: {
    ageRange: { min: 22, max: 35 },
    maxDistance: 50,
    genderPreference: 'everyone',
    showMe: 'everyone',
    dealBreakers: [],
    mustHaves: [],
  },
  privacy: {
    profileVisibility: 'public',
    showDistance: true,
    showAge: true,
    showLastSeen: false,
    allowMessagesFrom: 'matches',
    showOnlineStatus: true,
    incognitoMode: false,
  },
  notifications: {
    pushNotifications: true,
    emailNotifications: false,
    newMatches: true,
    newMessages: true,
    likes: true,
    superLikes: true,
    promotions: false,
    tips: true,
    soundEnabled: true,
    vibrationEnabled: true,
    quietHours: {
      enabled: false,
      startTime: '22:00',
      endTime: '08:00',
    },
  },
  account: {
    twoFactorAuth: false,
    loginAlerts: true,
    dataSharing: false,
    analytics: true,
    locationServices: true,
    autoRenewal: false,
    emailVerified: false,
    phoneVerified: false,
    backupEmail: undefined,
    recoveryPhone: undefined,
    sessionTimeout: 30,
    deviceManagement: true,
  },
  app: {
    language: 'en',
    theme: 'system',
    units: 'metric',
    currency: 'USD',
    dateFormat: 'MM/DD/YYYY',
    timeFormat: '12h',
    autoPlayVideos: true,
    reducedMotion: false,
    highContrast: false,
    fontSize: 'medium',
  },
});

export const useProfileStore = create<ProfileState>()(
  persist(
    (set, get) => ({
      ...initialState,

      loadProfile: async () => {
        set({ isLoading: true, error: null });

        try {
          // For demo, use default profile
          // In production, this would fetch from API
          const profile = createDefaultProfile();
          const settings = createDefaultSettings();

          // Sync with premium status
          get().updatePremiumStatus();
          const premiumStore = usePremiumStore.getState();
          profile.isPremium = premiumStore.isPremium;

          // Calculate completion
          const completion = get().calculateCompletion(profile);
          profile.profileCompletion = completion;

          set({
            profile,
            settings,
            isLoading: false
          });

          // Validate profile
          const validation = get().validateProfile(profile);
          set({ validation });

        } catch (error) {
          set({
            error: error instanceof Error ? error.message : 'Failed to load profile',
            isLoading: false
          });
        }
      },

      updateProfile: async (updates: Partial<UserProfile>) => {
        const currentProfile = get().profile;
        if (!currentProfile) return;

        set({ isUpdating: true, error: null });

        // Haptic feedback
        if (Platform.OS !== 'web') {
          Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
        }

        try {
          const updatedProfile = {
            ...currentProfile,
            ...updates,
            updatedAt: new Date(),
          };

          // Recalculate completion
          const completion = get().calculateCompletion(updatedProfile);
          updatedProfile.profileCompletion = completion;

          // In production, this would call the API
          // await profileService.updateProfile(updatedProfile);

          set({ 
            profile: updatedProfile,
            isUpdating: false 
          });

          // Validate updated profile
          const validation = get().validateProfile(updatedProfile);
          set({ validation });

        } catch (error) {
          set({ 
            error: error instanceof Error ? error.message : 'Failed to update profile',
            isUpdating: false 
          });
        }
      },

      updateSettings: async (settingsUpdates: Partial<ProfileSettings>) => {
        const currentSettings = get().settings;
        if (!currentSettings) return;

        set({ isUpdating: true, error: null });

        try {
          const updatedSettings = {
            ...currentSettings,
            ...settingsUpdates,
          };

          // Use settings sync service for real-time sync
          await settingsSyncService.updateSettings(updatedSettings, 'profile-store');

          set({
            settings: updatedSettings,
            isUpdating: false
          });

          // Haptic feedback
          if (Platform.OS !== 'web') {
            Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
          }

        } catch (error) {
          set({
            error: error instanceof Error ? error.message : 'Failed to update settings',
            isUpdating: false
          });
        }
      },

      resetSettings: async () => {
        set({ isUpdating: true, error: null });

        try {
          await settingsSyncService.resetToDefaults();
          const defaultSettings = await settingsSyncService.getSettings();

          if (defaultSettings) {
            set({
              settings: defaultSettings,
              isUpdating: false
            });
          }

          // Haptic feedback
          if (Platform.OS !== 'web') {
            Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Heavy);
          }

        } catch (error) {
          set({
            error: error instanceof Error ? error.message : 'Failed to reset settings',
            isUpdating: false
          });
        }
      },

      exportSettings: async (): Promise<string> => {
        try {
          return await settingsSyncService.exportSettings();
        } catch (error) {
          set({
            error: error instanceof Error ? error.message : 'Failed to export settings'
          });
          throw error;
        }
      },

      importSettings: async (jsonData: string) => {
        set({ isUpdating: true, error: null });

        try {
          await settingsSyncService.importSettings(jsonData);
          const importedSettings = await settingsSyncService.getSettings();

          if (importedSettings) {
            set({
              settings: importedSettings,
              isUpdating: false
            });
          }

          // Haptic feedback
          if (Platform.OS !== 'web') {
            Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
          }

        } catch (error) {
          set({
            error: error instanceof Error ? error.message : 'Failed to import settings',
            isUpdating: false
          });
          throw error;
        }
      },

      createSettingsBackup: async () => {
        try {
          await settingsSyncService.createBackup();

          // Haptic feedback
          if (Platform.OS !== 'web') {
            Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
          }

        } catch (error) {
          set({
            error: error instanceof Error ? error.message : 'Failed to create backup'
          });
          throw error;
        }
      },

      restoreSettingsBackup: async () => {
        set({ isUpdating: true, error: null });

        try {
          await settingsSyncService.restoreFromBackup();
          const restoredSettings = await settingsSyncService.getSettings();

          if (restoredSettings) {
            set({
              settings: restoredSettings,
              isUpdating: false
            });
          }

          // Haptic feedback
          if (Platform.OS !== 'web') {
            Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
          }

        } catch (error) {
          set({
            error: error instanceof Error ? error.message : 'Failed to restore backup',
            isUpdating: false
          });
          throw error;
        }
      },

      getSettingsHistory: async () => {
        try {
          return await settingsSyncService.getSettingsHistory();
        } catch (error) {
          set({
            error: error instanceof Error ? error.message : 'Failed to get settings history'
          });
          return [];
        }
      },

      uploadPhoto: async (photoRequest: PhotoUploadRequest): Promise<string> => {
        set({ isUploadingPhoto: true, error: null });

        try {
          // In production, this would upload to cloud storage
          const photoId = `photo-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
          
          const currentProfile = get().profile;
          if (!currentProfile) throw new Error('No profile loaded');

          const newPhoto: ProfilePhoto = {
            id: photoId,
            url: photoRequest.uri,
            order: photoRequest.order || currentProfile.photos.length,
            isMain: photoRequest.isMain || false,
            uploadedAt: new Date(),
            verificationStatus: 'none',
          };

          const updatedPhotos = [...currentProfile.photos, newPhoto];
          
          await get().updateProfile({ photos: updatedPhotos });
          
          set({ isUploadingPhoto: false });
          return photoId;

        } catch (error) {
          set({ 
            error: error instanceof Error ? error.message : 'Failed to upload photo',
            isUploadingPhoto: false 
          });
          throw error;
        }
      },

      deletePhoto: async (photoId: string) => {
        const currentProfile = get().profile;
        if (!currentProfile) return;

        try {
          // await profileService.deletePhoto(photoId);
          
          const updatedPhotos = currentProfile.photos.filter(photo => photo.id !== photoId);
          await get().updateProfile({ photos: updatedPhotos });

        } catch (error) {
          set({ 
            error: error instanceof Error ? error.message : 'Failed to delete photo'
          });
        }
      },

      reorderPhotos: async (photoIds: string[]) => {
        const currentProfile = get().profile;
        if (!currentProfile) return;

        try {
          const reorderedPhotos = photoIds.map((id, index) => {
            const photo = currentProfile.photos.find(p => p.id === id);
            return photo ? { ...photo, order: index } : null;
          }).filter(Boolean) as ProfilePhoto[];

          await get().updateProfile({ photos: reorderedPhotos });

        } catch (error) {
          set({ 
            error: error instanceof Error ? error.message : 'Failed to reorder photos'
          });
        }
      },

      setMainPhoto: async (photoId: string) => {
        const currentProfile = get().profile;
        if (!currentProfile) return;

        try {
          const updatedPhotos = currentProfile.photos.map(photo => ({
            ...photo,
            isMain: photo.id === photoId,
          }));

          await get().updateProfile({ photos: updatedPhotos });

        } catch (error) {
          set({
            error: error instanceof Error ? error.message : 'Failed to set main photo'
          });
        }
      },

      editPhoto: async (photoId: string, editedUri: string) => {
        const currentProfile = get().profile;
        if (!currentProfile) return;

        try {
          const updatedPhotos = currentProfile.photos.map(photo =>
            photo.id === photoId
              ? { ...photo, url: editedUri, editedAt: new Date() }
              : photo
          );

          await get().updateProfile({ photos: updatedPhotos });
        } catch (error) {
          set({
            error: error instanceof Error ? error.message : 'Failed to edit photo'
          });
        }
      },

      verifyPhoto: async (photoId: string) => {
        const currentProfile = get().profile;
        if (!currentProfile) return;

        try {
          // Set status to pending
          const updatedPhotos = currentProfile.photos.map(photo =>
            photo.id === photoId
              ? { ...photo, verificationStatus: 'pending' as const }
              : photo
          );

          await get().updateProfile({ photos: updatedPhotos });

          // Simulate verification process
          setTimeout(async () => {
            const verificationResult = Math.random() > 0.2 ? 'verified' : 'rejected';

            const finalPhotos = get().profile?.photos.map(photo =>
              photo.id === photoId
                ? { ...photo, verificationStatus: verificationResult as 'verified' | 'rejected' }
                : photo
            ) || [];

            await get().updateProfile({ photos: finalPhotos });
          }, 3000);
        } catch (error) {
          set({
            error: error instanceof Error ? error.message : 'Failed to verify photo'
          });
        }
      },

      validateProfile: (profile?: Partial<UserProfile>): ProfileValidation => {
        const targetProfile = profile || get().profile;
        if (!targetProfile) {
          return {
            isValid: false,
            errors: { profile: ['Profile not loaded'] },
            warnings: {},
          };
        }

        const errors: { [key: string]: string[] } = {};
        const warnings: { [key: string]: string[] } = {};

        // Validate bio
        if (!targetProfile.bio || targetProfile.bio.length < 10) {
          errors.bio = ['Bio must be at least 10 characters long'];
        } else if (targetProfile.bio.length > 500) {
          errors.bio = ['Bio must be less than 500 characters'];
        }

        // Validate photos
        if (!targetProfile.photos || targetProfile.photos.length < 2) {
          errors.photos = ['At least 2 photos are required'];
        }

        // Validate interests
        if (!targetProfile.interests || targetProfile.interests.length < 3) {
          warnings.interests = ['Add more interests to improve your matches'];
        }

        return {
          isValid: Object.keys(errors).length === 0,
          errors,
          warnings,
        };
      },

      calculateCompletion: (profile?: UserProfile): number => {
        const targetProfile = profile || get().profile;
        if (!targetProfile) return 0;

        let score = 0;
        const weights = PROFILE_COMPLETION_WEIGHTS;

        // Photos
        if (targetProfile.photos && targetProfile.photos.length >= 2) {
          score += weights.photos;
        }

        // Bio
        if (targetProfile.bio && targetProfile.bio.length >= 10) {
          score += weights.bio;
        }

        // Interests
        if (targetProfile.interests && targetProfile.interests.length >= 3) {
          score += weights.interests;
        }

        // Occupation
        if (targetProfile.occupation) {
          score += weights.occupation;
        }

        // Education
        if (targetProfile.education) {
          score += weights.education;
        }

        // Location
        if (targetProfile.location && targetProfile.location.city) {
          score += weights.location;
        }

        // Social media
        if (targetProfile.socialMedia && Object.keys(targetProfile.socialMedia).length > 0) {
          score += weights.socialMedia;
        }

        return Math.min(score, 100);
      },

      loadAnalytics: async () => {
        try {
          // In production, this would fetch from API
          const analytics: ProfileAnalytics = {
            profileViews: 156,
            likesReceived: 23,
            matchesCount: 8,
            messagesReceived: 45,
            profileViewsThisWeek: 34,
            popularPhotos: ['photo-1', 'photo-2'],
            peakActivityHours: [19, 20, 21],
            averageResponseTime: 45,
          };

          set({ analytics });
        } catch (error) {
          console.error('Failed to load analytics:', error);
        }
      },

      trackProfileView: () => {
        // In production, this would send analytics event
        console.log('Profile view tracked');
      },

      // Premium integration
      updatePremiumStatus: () => {
        const premiumStore = usePremiumStore.getState();
        const currentProfile = get().profile;

        if (currentProfile && currentProfile.isPremium !== premiumStore.isPremium) {
          set({
            profile: {
              ...currentProfile,
              isPremium: premiumStore.isPremium,
            }
          });
        }
      },

      getPremiumBadgeInfo: () => {
        const premiumStore = usePremiumStore.getState();
        const subscription = premiumStore.subscription;

        if (premiumStore.isPremium && subscription) {
          const plan = premiumStore.availablePlans.find(p => p.id === subscription.planId);
          return {
            isPremium: true,
            badgeText: plan?.name || 'Premium',
          };
        }

        return { isPremium: false };
      },

      clearError: () => set({ error: null }),

      reset: () => set(initialState),
    }),
    {
      name: 'profile-storage',
      storage: createJSONStorage(() => AsyncStorage),
      partialize: (state) => ({
        profile: state.profile,
        settings: state.settings,
      }),
    }
  )
);
