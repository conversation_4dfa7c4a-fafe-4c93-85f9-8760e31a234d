import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
  Switch,
  Alert,
  Platform,
  Modal,
  TextInput,
  Linking,
  Share,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { useRouter } from 'expo-router';
import { useProfileStore } from '@/stores/profileStore';
import { usePremiumStore } from '@/stores/premiumStore';
import { theme } from '@/constants/theme';
import RangePicker from '../../components/ui/RangeSlider';
import { triggerHaptic } from '../../utils/haptics';
import TimePicker from '../../components/ui/TimePicker';
import DistancePicker from '../../components/ui/DistancePicker';
import SessionTimeoutPicker from '../../components/ui/SessionTimeoutPicker';
import SettingsModal from '../../components/ui/SettingsModal';
// import SettingsSearch from '../../components/ui/SettingsSearch';
// import SettingsSyncManager from '../../components/settings/SettingsSyncManager';
// import WebCompatibilityTest from '../../components/test/WebCompatibilityTest';
import LoadingSkeleton from '../../components/LoadingSkeleton';
import { settingsSyncService } from '../../services/settingsSync';
import { pushNotificationService } from '../../services/pushNotifications';
import ProfileNavigationService from '../../services/profileNavigation';
import * as Notifications from 'expo-notifications';
import * as DocumentPicker from 'expo-document-picker';
import * as FileSystem from 'expo-file-system';
import * as Sharing from 'expo-sharing';
import {
  ArrowLeft,
  Heart,
  Shield,
  Bell,
  User,
  MapPin,
  Eye,
  MessageCircle,
  Moon,
  Vibrate,
  Volume2,
  Trash2,
  ChevronRight,
  Settings,
  Award,
  Info,
  Search,
  Download,
  Upload,
  RotateCcw,
  Filter,
  Clock,
  Check,
  X,
  TestTube,
  Globe,
  Smartphone,
  Mail,
  HelpCircle,
  Edit3,
  Camera,
  BarChart3,
} from 'lucide-react-native';

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  safeArea: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    paddingTop: 20,
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 20,
    fontFamily: 'Inter-Bold',
    color: 'white',
    flex: 1,
    textAlign: 'center',
    marginHorizontal: 16,
  },
  advancedButton: {
    padding: 8,
  },
  syncContainer: {
    paddingHorizontal: 20,
    paddingBottom: 16,
  },
  scrollView: {
    flex: 1,
    backgroundColor: 'white',
    borderTopLeftRadius: 24,
    borderTopRightRadius: 24,
    paddingTop: 24,
  },
  section: {
    marginBottom: 32,
  },
  sectionTitle: {
    fontSize: 18,
    fontFamily: 'Inter-Bold',
    color: theme.colors.text,
    marginBottom: 16,
    paddingHorizontal: 20,
  },
  settingItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.gray100,
  },
  settingLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  settingIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: theme.colors.gray50,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 16,
  },
  settingContent: {
    flex: 1,
  },
  settingTitle: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: theme.colors.text,
    marginBottom: 4,
  },
  settingSubtitle: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: theme.colors.gray600,
  },
  searchContainer: {
    paddingHorizontal: 20,
    paddingBottom: 16,
  },
  genderModal: {
    backgroundColor: 'white',
    borderRadius: 16,
    padding: 24,
    margin: 20,
    maxHeight: '80%',
  },
  genderModalTitle: {
    fontSize: 20,
    fontFamily: 'Inter-Bold',
    color: theme.colors.text,
    marginBottom: 20,
    textAlign: 'center',
  },
  genderOption: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 16,
    paddingHorizontal: 20,
    borderRadius: 12,
    marginBottom: 12,
    backgroundColor: theme.colors.gray100,
  },
  genderOptionSelected: {
    backgroundColor: theme.colors.primary,
  },
  genderOptionText: {
    fontSize: 16,
    fontFamily: 'Inter-Medium',
    color: theme.colors.text,
  },
  genderOptionTextSelected: {
    color: 'white',
  },
  quietHoursContainer: {
    paddingVertical: 16,
  },
  timePickerContainer: {
    marginBottom: 24,
  },
  timePickerLabel: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: theme.colors.text,
    marginBottom: 12,
  },
  // FAQ styles
  faqContainer: {
    flex: 1,
  },
  faqItem: {
    marginBottom: 24,
    paddingBottom: 16,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.gray200,
  },
  faqQuestion: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: theme.colors.text,
    marginBottom: 8,
  },
  faqAnswer: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: theme.colors.gray600,
    lineHeight: 20,
  },
  // Form styles
  contactForm: {
    flex: 1,
  },
  feedbackForm: {
    flex: 1,
  },
  formLabel: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: theme.colors.text,
    marginBottom: 8,
  },
  formInput: {
    borderWidth: 1,
    borderColor: theme.colors.gray300,
    borderRadius: 12,
    padding: 16,
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    color: theme.colors.text,
    backgroundColor: 'white',
    marginBottom: 16,
  },
  formTextArea: {
    height: 120,
    textAlignVertical: 'top',
  },
  // Delete account styles
  deleteAccountForm: {
    flex: 1,
  },
  deleteWarning: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: theme.colors.error,
    textAlign: 'center',
    marginBottom: 24,
    padding: 16,
    backgroundColor: `${theme.colors.error}10`,
    borderRadius: 12,
  },
  deleteInstructions: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: theme.colors.text,
    marginBottom: 16,
  },
  deleteConfirmInput: {
    borderWidth: 2,
    borderColor: theme.colors.error,
    borderRadius: 12,
    padding: 16,
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    color: theme.colors.text,
    backgroundColor: 'white',
    marginBottom: 16,
  },
  deletingIndicator: {
    alignItems: 'center',
    padding: 16,
  },
  deletingText: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
    color: theme.colors.gray600,
  },
  // Subscription styles
  subscriptionDetails: {
    padding: 16,
  },
  subscriptionPlan: {
    fontSize: 20,
    fontFamily: 'Inter-Bold',
    color: theme.colors.text,
    marginBottom: 8,
  },
  subscriptionStatus: {
    fontSize: 16,
    fontFamily: 'Inter-Medium',
    color: theme.colors.primary,
    marginBottom: 8,
  },
  subscriptionExpiry: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: theme.colors.gray600,
    marginBottom: 8,
  },
  subscriptionRenewal: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: theme.colors.success,
  },
  noSubscription: {
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    color: theme.colors.gray600,
    textAlign: 'center',
  },
  // Search and sync styles
  searchContainer: {
    marginBottom: 16,
  },
  searchInput: {
    borderWidth: 1,
    borderColor: theme.colors.gray300,
    borderRadius: 12,
    padding: 16,
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    color: theme.colors.text,
    backgroundColor: 'white',
  },
  syncContainer: {
    marginBottom: 16,
  },
  syncStatus: {
    padding: 12,
    backgroundColor: theme.colors.success + '20',
    borderRadius: 8,
    alignItems: 'center',
  },
  syncStatusText: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
    color: theme.colors.success,
  },
});

const SettingItem = ({
  icon: Icon,
  title,
  subtitle,
  value,
  onToggle,
  type = 'switch',
  onPress
}: {
  icon: any;
  title: string;
  subtitle?: string;
  value?: boolean;
  onToggle?: (value: boolean) => void;
  type?: 'switch' | 'button';
  onPress?: () => void;
}) => (
  <TouchableOpacity
    style={styles.settingItem}
    onPress={type === 'button' ? onPress : undefined}
    disabled={type === 'switch'}
  >
    <View style={styles.settingLeft}>
      <View style={styles.settingIcon}>
        <Icon size={20} color={theme.colors.primary} />
      </View>
      <View style={styles.settingContent}>
        <Text style={styles.settingTitle}>{title}</Text>
        {subtitle && <Text style={styles.settingSubtitle}>{subtitle}</Text>}
      </View>
    </View>
    {type === 'switch' && onToggle && (
      <Switch
        value={value}
        onValueChange={onToggle}
        trackColor={{ false: theme.colors.gray300, true: theme.colors.primary + '40' }}
        thumbColor={value ? theme.colors.primary : theme.colors.gray400}
      />
    )}
    {type === 'button' && (
      <ChevronRight size={20} color={theme.colors.gray400} />
    )}
  </TouchableOpacity>
);

export default function ProfileSettingsScreen() {
  const router = useRouter();
  const { settings, updateSettings, isUpdating, isLoading, profile } = useProfileStore();
  const { subscription, isPremium, availablePlans, isLoading: premiumLoading } = usePremiumStore();

  const [localSettings, setLocalSettings] = useState({
    // Dating preferences
    ageMin: 22,
    ageMax: 35,
    maxDistance: 50,
    genderPreference: 'everyone' as 'men' | 'women' | 'everyone',
    
    // Privacy settings
    profileVisibility: 'public' as 'public' | 'private' | 'friends',
    showDistance: true,
    showAge: true,
    showLastSeen: false,
    allowMessagesFrom: 'matches' as 'everyone' | 'matches' | 'premium',
    showOnlineStatus: true,
    incognitoMode: false,
    
    // Notification settings
    pushNotifications: true,
    emailNotifications: false,
    newMatches: true,
    newMessages: true,
    likes: true,
    superLikes: true,
    promotions: false,
    tips: true,
    soundEnabled: true,
    vibrationEnabled: true,
    quietHoursEnabled: false,
    quietHoursStart: '22:00',
    quietHoursEnd: '08:00',
    
    // Account settings
    twoFactorAuth: false,
    loginAlerts: true,
    dataSharing: false,
    analytics: true,
    locationServices: true,
    autoRenewal: false,
  });

  // Modal states
  const [showAgeRangeModal, setShowAgeRangeModal] = useState(false);
  const [showDistanceModal, setShowDistanceModal] = useState(false);
  const [showGenderModal, setShowGenderModal] = useState(false);
  const [showQuietHoursModal, setShowQuietHoursModal] = useState(false);
  const [showSessionTimeoutModal, setShowSessionTimeoutModal] = useState(false);
  const [showLanguageModal, setShowLanguageModal] = useState(false);
  const [showThemeModal, setShowThemeModal] = useState(false);
  const [showUnitsModal, setShowUnitsModal] = useState(false);
  const [showMessageSettingsModal, setShowMessageSettingsModal] = useState(false);
  const [showPrivacyModal, setShowPrivacyModal] = useState(false);
  const [showSubscriptionModal, setShowSubscriptionModal] = useState(false);
  const [showHelpModal, setShowHelpModal] = useState(false);
  const [showFAQModal, setShowFAQModal] = useState(false);
  const [showContactModal, setShowContactModal] = useState(false);
  const [showFeedbackModal, setShowFeedbackModal] = useState(false);
  const [showDeleteAccountModal, setShowDeleteAccountModal] = useState(false);
  const [showDataExportModal, setShowDataExportModal] = useState(false);

  // Search and filter states
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategories, setSelectedCategories] = useState<string[]>([]);
  const [showAdvancedSettings, setShowAdvancedSettings] = useState(false);
  const [showWebTest, setShowWebTest] = useState(false);

  // Loading states
  const [isExporting, setIsExporting] = useState(false);
  const [isImporting, setIsImporting] = useState(false);
  const [isTestingNotifications, setIsTestingNotifications] = useState(false);
  const [isDeletingAccount, setIsDeletingAccount] = useState(false);

  // Form states
  const [feedbackText, setFeedbackText] = useState('');
  const [contactSubject, setContactSubject] = useState('');
  const [contactMessage, setContactMessage] = useState('');
  const [deleteConfirmText, setDeleteConfirmText] = useState('');

  // Temporary values for modals
  const [tempAgeRange, setTempAgeRange] = useState({ min: 22, max: 35 });
  const [tempDistance, setTempDistance] = useState(50);
  const [tempGender, setTempGender] = useState('everyone');
  const [tempQuietHours, setTempQuietHours] = useState({ start: '22:00', end: '08:00' });
  const [tempSessionTimeout, setTempSessionTimeout] = useState(30);
  const [tempLanguage, setTempLanguage] = useState('en');
  const [tempTheme, setTempTheme] = useState('system');
  const [tempUnits, setTempUnits] = useState('metric');
  const [tempMessageSettings, setTempMessageSettings] = useState('matches');
  const [tempPrivacySettings, setTempPrivacySettings] = useState('public');

  const settingsCategories = [
    'Dating Preferences',
    'Privacy & Safety',
    'Notifications',
    'Account & Security',
    'App Preferences',
    'Subscription',
    'Help & Support',
    'Advanced'
  ];

  const languages = [
    { code: 'en', name: 'English', flag: '🇺🇸' },
    { code: 'es', name: 'Español', flag: '🇪🇸' },
    { code: 'fr', name: 'Français', flag: '🇫🇷' },
    { code: 'de', name: 'Deutsch', flag: '🇩🇪' },
    { code: 'it', name: 'Italiano', flag: '🇮🇹' },
    { code: 'pt', name: 'Português', flag: '🇵🇹' },
    { code: 'ru', name: 'Русский', flag: '🇷🇺' },
    { code: 'ja', name: '日本語', flag: '🇯🇵' },
    { code: 'ko', name: '한국어', flag: '🇰🇷' },
    { code: 'zh', name: '中文', flag: '🇨🇳' },
  ];

  const themes = [
    { value: 'light', name: 'Light', description: 'Light theme for daytime use' },
    { value: 'dark', name: 'Dark', description: 'Dark theme for nighttime use' },
    { value: 'system', name: 'System', description: 'Follow system settings' },
  ];

  const units = [
    { value: 'metric', name: 'Metric', description: 'Kilometers, Celsius' },
    { value: 'imperial', name: 'Imperial', description: 'Miles, Fahrenheit' },
  ];

  const faqItems = [
    {
      question: 'How do I change my profile picture?',
      answer: 'Go to your profile, tap on your current photo, and select "Change Photo" to upload a new one.',
    },
    {
      question: 'How does matching work?',
      answer: 'When you and another user both like each other, you create a match and can start messaging.',
    },
    {
      question: 'Can I undo a swipe?',
      answer: 'Premium users can use the Rewind feature to undo their last swipe. Free users cannot undo swipes.',
    },
    {
      question: 'How do I report someone?',
      answer: 'On their profile or in chat, tap the menu (⋯) and select "Report". Choose the appropriate reason.',
    },
    {
      question: 'How do I delete my account?',
      answer: 'Go to Settings > Danger Zone > Delete Account. This action cannot be undone.',
    },
  ];

  useEffect(() => {
    if (settings) {
      setLocalSettings({
        ageMin: settings.dating.ageRange.min,
        ageMax: settings.dating.ageRange.max,
        maxDistance: settings.dating.maxDistance,
        genderPreference: settings.dating.genderPreference,
        profileVisibility: settings.privacy.profileVisibility,
        showDistance: settings.privacy.showDistance,
        showAge: settings.privacy.showAge,
        showLastSeen: settings.privacy.showLastSeen,
        allowMessagesFrom: settings.privacy.allowMessagesFrom,
        showOnlineStatus: settings.privacy.showOnlineStatus,
        incognitoMode: settings.privacy.incognitoMode,
        pushNotifications: settings.notifications.pushNotifications,
        emailNotifications: settings.notifications.emailNotifications,
        newMatches: settings.notifications.newMatches,
        newMessages: settings.notifications.newMessages,
        likes: settings.notifications.likes,
        superLikes: settings.notifications.superLikes,
        promotions: settings.notifications.promotions,
        tips: settings.notifications.tips,
        soundEnabled: settings.notifications.soundEnabled,
        vibrationEnabled: settings.notifications.vibrationEnabled,
        quietHoursEnabled: settings.notifications.quietHours.enabled,
        quietHoursStart: settings.notifications.quietHours.startTime,
        quietHoursEnd: settings.notifications.quietHours.endTime,
        twoFactorAuth: settings.account.twoFactorAuth,
        loginAlerts: settings.account.loginAlerts,
        dataSharing: settings.account.dataSharing,
        analytics: settings.account.analytics,
        locationServices: settings.account.locationServices,
        autoRenewal: settings.account.autoRenewal,
        sessionTimeout: settings.account.sessionTimeout,
      });

      // Update temp values for modals
      setTempLanguage(settings.app.language);
      setTempTheme(settings.app.theme);
      setTempUnits(settings.app.units);
      setTempMessageSettings(settings.privacy.allowMessagesFrom);
    }
  }, [settings]);

  // Initialize settings sync service
  useEffect(() => {
    const initializeSettings = async () => {
      try {
        const cachedSettings = await settingsSyncService.getSettings();
        if (cachedSettings && !settings) {
          await updateSettings(cachedSettings);
        }
      } catch (error) {
        console.error('Failed to initialize settings:', error);
      }
    };

    initializeSettings();
  }, []);

  const handleToggle = async (key: string, value: boolean) => {
    triggerHaptic.light();

    setLocalSettings(prev => ({ ...prev, [key]: value }));

    // Update the store immediately for better UX
    if (settings) {
      const updatedSettings = { ...settings };
      
      // Map the local setting to the correct nested structure
      if (key === 'ageMin' || key === 'ageMax') {
        updatedSettings.dating.ageRange = {
          min: key === 'ageMin' ? value as any : localSettings.ageMin,
          max: key === 'ageMax' ? value as any : localSettings.ageMax,
        };
      } else if (key === 'maxDistance' || key === 'genderPreference') {
        (updatedSettings.dating as any)[key] = value;
      } else if (['profileVisibility', 'showDistance', 'showAge', 'showLastSeen', 'allowMessagesFrom', 'showOnlineStatus', 'incognitoMode'].includes(key)) {
        (updatedSettings.privacy as any)[key] = value;
      } else if (['pushNotifications', 'emailNotifications', 'newMatches', 'newMessages', 'likes', 'superLikes', 'promotions', 'tips', 'soundEnabled', 'vibrationEnabled'].includes(key)) {
        (updatedSettings.notifications as any)[key] = value;
      } else if (key === 'quietHoursEnabled') {
        updatedSettings.notifications.quietHours.enabled = value;
      } else if (['twoFactorAuth', 'loginAlerts', 'dataSharing', 'analytics', 'locationServices', 'autoRenewal'].includes(key)) {
        (updatedSettings.account as any)[key] = value;
      }

      await updateSettings(updatedSettings);
    }
  };

  // Enhanced toggle handler with notification support
  const handleNotificationToggle = async (key: string, value: boolean) => {
    triggerHaptic.light();

    // Handle push notification permission
    if (key === 'pushNotifications' && value) {
      await requestNotificationPermission();
    }

    await handleToggle(key, value);

    // Sync settings to service
    if (settings) {
      await settingsSyncService.updateSettings(settings);
    }
  };

  // Request notification permission
  const requestNotificationPermission = async () => {
    try {
      const token = await pushNotificationService.initialize();
      if (token) {
        console.log('Push notification token:', token);
      }
    } catch (error) {
      console.error('Failed to initialize push notifications:', error);
      Alert.alert(
        'Notification Permission',
        'Unable to enable push notifications. Please check your device settings.',
        [{ text: 'OK' }]
      );
    }
  };

  // Test notification functionality
  const testNotification = async () => {
    setIsTestingNotifications(true);
    triggerHaptic.medium();

    try {
      await Notifications.scheduleNotificationAsync({
        content: {
          title: 'Test Notification',
          body: 'This is a test notification from your dating app!',
          sound: localSettings.soundEnabled,
        },
        trigger: { seconds: 1 },
      });

      Alert.alert(
        'Test Notification Sent',
        'You should receive a test notification in a moment.',
        [{ text: 'OK' }]
      );
    } catch (error) {
      console.error('Failed to send test notification:', error);
      Alert.alert(
        'Test Failed',
        'Unable to send test notification. Please check your notification settings.',
        [{ text: 'OK' }]
      );
    } finally {
      setIsTestingNotifications(false);
    }
  };

  const handleDeleteAccount = () => {
    triggerHaptic.heavy();
    setShowDeleteAccountModal(true);
  };

  const confirmDeleteAccount = async () => {
    if (deleteConfirmText.toLowerCase() !== 'delete') {
      Alert.alert('Confirmation Required', 'Please type "delete" to confirm account deletion.');
      return;
    }

    setIsDeletingAccount(true);
    triggerHaptic.heavy();

    try {
      // Call account deletion API
      const response = await fetch('/api/users/delete', {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          userId: profile?.id,
          confirmationText: deleteConfirmText,
        }),
      });

      if (response.ok) {
        // Clear all local data
        await settingsSyncService.clearAllData();

        // Clear profile store
        const profileStore = useProfileStore.getState();
        profileStore.clearProfile();

        Alert.alert(
          'Account Deleted',
          'Your account has been permanently deleted. All your data has been removed.',
          [
            {
              text: 'OK',
              onPress: () => {
                // Navigate to auth screen
                router.replace('/auth/login');
              }
            }
          ]
        );
      } else {
        throw new Error('Failed to delete account');
      }
    } catch (error) {
      console.error('Failed to delete account:', error);
      Alert.alert(
        'Deletion Failed',
        'Unable to delete your account. Please try again or contact support.',
        [{ text: 'OK' }]
      );
    } finally {
      setIsDeletingAccount(false);
      setShowDeleteAccountModal(false);
      setDeleteConfirmText('');
    }
  };

  // Data export functionality
  const handleDataExport = async () => {
    setIsExporting(true);
    triggerHaptic.medium();

    try {
      const exportData = await settingsSyncService.exportSettings();

      if (Platform.OS === 'web') {
        // Web download
        const blob = new Blob([exportData], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `dating_app_data_${new Date().toISOString().split('T')[0]}.json`;
        a.click();
        URL.revokeObjectURL(url);
      } else {
        // Mobile sharing using expo-sharing
        const fileName = `dating_app_data_${new Date().toISOString().split('T')[0]}.json`;
        const fileUri = `${FileSystem.documentDirectory}${fileName}`;
        await FileSystem.writeAsStringAsync(fileUri, exportData);

        if (await Sharing.isAvailableAsync()) {
          await Sharing.shareAsync(fileUri, {
            mimeType: 'application/json',
            dialogTitle: 'Export Your Data',
          });
        } else {
          // Fallback to Share API
          try {
            await Share.share({
              message: `Your dating app data export:\n\n${exportData}`,
              title: 'Export Your Data',
            });
          } catch (error) {
            Alert.alert('Export Complete', `Data exported to: ${fileUri}`);
          }
        }
      }

      Alert.alert(
        'Data Exported',
        'Your data has been exported successfully.',
        [{ text: 'OK' }]
      );
    } catch (error) {
      console.error('Failed to export data:', error);
      Alert.alert(
        'Export Failed',
        'Unable to export your data. Please try again.',
        [{ text: 'OK' }]
      );
    } finally {
      setIsExporting(false);
    }
  };

  // Language selection handler
  const handleLanguagePress = () => {
    triggerHaptic.light();
    setTempLanguage(settings?.app.language || 'en');
    setShowLanguageModal(true);
  };

  const saveLanguage = async () => {
    // Validate language selection
    const validLanguages = languages.map(l => l.code);
    if (!validLanguages.includes(tempLanguage)) {
      Alert.alert('Invalid Language', 'Please select a valid language.');
      return;
    }

    try {
      if (settings) {
        const updatedSettings = {
          ...settings,
          app: { ...settings.app, language: tempLanguage }
        };
        await updateSettings(updatedSettings);
        await settingsSyncService.updateSettings(updatedSettings);
      }
      triggerHaptic.medium();
    } catch (error) {
      console.error('Failed to save language:', error);
      Alert.alert('Save Failed', 'Unable to save language preference. Please try again.');
    }
  };

  // Theme selection handler
  const handleThemePress = () => {
    triggerHaptic.light();
    setTempTheme(settings?.app.theme || 'system');
    setShowThemeModal(true);
  };

  const saveTheme = async () => {
    // Validate theme selection
    const validThemes = themes.map(t => t.value);
    if (!validThemes.includes(tempTheme)) {
      Alert.alert('Invalid Theme', 'Please select a valid theme.');
      return;
    }

    try {
      if (settings) {
        const updatedSettings = {
          ...settings,
          app: { ...settings.app, theme: tempTheme }
        };
        await updateSettings(updatedSettings);
        await settingsSyncService.updateSettings(updatedSettings);
      }
      triggerHaptic.medium();
    } catch (error) {
      console.error('Failed to save theme:', error);
      Alert.alert('Save Failed', 'Unable to save theme preference. Please try again.');
    }
  };

  // Units selection handler
  const handleUnitsPress = () => {
    triggerHaptic.light();
    setTempUnits(settings?.app.units || 'metric');
    setShowUnitsModal(true);
  };

  const saveUnits = async () => {
    // Validate units selection
    const validUnits = units.map(u => u.value);
    if (!validUnits.includes(tempUnits)) {
      Alert.alert('Invalid Units', 'Please select a valid unit system.');
      return;
    }

    try {
      if (settings) {
        const updatedSettings = {
          ...settings,
          app: { ...settings.app, units: tempUnits }
        };
        await updateSettings(updatedSettings);
        await settingsSyncService.updateSettings(updatedSettings);
      }
      triggerHaptic.medium();
    } catch (error) {
      console.error('Failed to save units:', error);
      Alert.alert('Save Failed', 'Unable to save units preference. Please try again.');
    }
  };

  // Message settings handler
  const handleMessageSettingsPress = () => {
    triggerHaptic.light();
    setTempMessageSettings(settings?.privacy.allowMessagesFrom || 'matches');
    setShowMessageSettingsModal(true);
  };

  const saveMessageSettings = async () => {
    // Validate message settings
    const validSettings = ['everyone', 'matches'];
    if (!validSettings.includes(tempMessageSettings)) {
      Alert.alert('Invalid Setting', 'Please select a valid message setting.');
      return;
    }

    try {
      if (settings) {
        const updatedSettings = {
          ...settings,
          privacy: { ...settings.privacy, allowMessagesFrom: tempMessageSettings as any }
        };
        await updateSettings(updatedSettings);
        await settingsSyncService.updateSettings(updatedSettings);
      }
      triggerHaptic.medium();
    } catch (error) {
      console.error('Failed to save message settings:', error);
      Alert.alert('Save Failed', 'Unable to save message settings. Please try again.');
    }
  };

  // Enhanced modal handlers
  const handleAgeRangePress = () => {
    triggerHaptic.light();
    setTempAgeRange({ min: localSettings.ageMin, max: localSettings.ageMax });
    setShowAgeRangeModal(true);
  };

  const handleDistancePress = () => {
    triggerHaptic.light();
    setTempDistance(localSettings.maxDistance);
    setShowDistanceModal(true);
  };

  const handleGenderPress = () => {
    triggerHaptic.light();
    setTempGender(localSettings.genderPreference);
    setShowGenderModal(true);
  };

  const handleQuietHoursPress = () => {
    triggerHaptic.light();
    setTempQuietHours({
      start: localSettings.quietHoursStart,
      end: localSettings.quietHoursEnd
    });
    setShowQuietHoursModal(true);
  };

  const handleSessionTimeoutPress = () => {
    triggerHaptic.light();
    setTempSessionTimeout(localSettings.sessionTimeout || 30);
    setShowSessionTimeoutModal(true);
  };

  // Save handlers for modals
  const saveAgeRange = async () => {
    // Validate age range
    if (tempAgeRange.min < 18 || tempAgeRange.max > 100) {
      Alert.alert('Invalid Age Range', 'Age must be between 18 and 100 years.');
      return;
    }

    if (tempAgeRange.min >= tempAgeRange.max) {
      Alert.alert('Invalid Age Range', 'Minimum age must be less than maximum age.');
      return;
    }

    try {
      setLocalSettings(prev => ({
        ...prev,
        ageMin: tempAgeRange.min,
        ageMax: tempAgeRange.max
      }));

      if (settings) {
        const updatedSettings = {
          ...settings,
          dating: {
            ...settings.dating,
            ageRange: { min: tempAgeRange.min, max: tempAgeRange.max }
          }
        };
        await updateSettings(updatedSettings);
        await settingsSyncService.updateSettings(updatedSettings);
      }

      triggerHaptic.medium();
    } catch (error) {
      console.error('Failed to save age range:', error);
      Alert.alert('Save Failed', 'Unable to save age range. Please try again.');
    }
  };

  const saveDistance = async () => {
    // Validate distance range
    if (tempDistance < 1 || tempDistance > 500) {
      Alert.alert('Invalid Distance', 'Distance must be between 1 and 500 km.');
      return;
    }

    try {
      setLocalSettings(prev => ({ ...prev, maxDistance: tempDistance }));

      if (settings) {
        const updatedSettings = {
          ...settings,
          dating: {
            ...settings.dating,
            maxDistance: tempDistance
          }
        };
        await updateSettings(updatedSettings);
        await settingsSyncService.updateSettings(updatedSettings);
      }

      triggerHaptic.medium();
    } catch (error) {
      console.error('Failed to save distance:', error);
      Alert.alert('Save Failed', 'Unable to save distance preference. Please try again.');
    }
  };

  const saveGender = async () => {
    // Validate gender preference
    const validGenders = ['men', 'women', 'everyone'];
    if (!validGenders.includes(tempGender)) {
      Alert.alert('Invalid Selection', 'Please select a valid gender preference.');
      return;
    }

    try {
      setLocalSettings(prev => ({ ...prev, genderPreference: tempGender as any }));

      if (settings) {
        const updatedSettings = {
          ...settings,
          dating: {
            ...settings.dating,
            genderPreference: tempGender as any
          }
        };
        await updateSettings(updatedSettings);
        await settingsSyncService.updateSettings(updatedSettings);
      }

      triggerHaptic.medium();
    } catch (error) {
      console.error('Failed to save gender preference:', error);
      Alert.alert('Save Failed', 'Unable to save gender preference. Please try again.');
    }
  };

  const saveQuietHours = async () => {
    setLocalSettings(prev => ({
      ...prev,
      quietHoursStart: tempQuietHours.start,
      quietHoursEnd: tempQuietHours.end
    }));

    if (settings) {
      const updatedSettings = {
        ...settings,
        notifications: {
          ...settings.notifications,
          quietHours: {
            ...settings.notifications.quietHours,
            startTime: tempQuietHours.start,
            endTime: tempQuietHours.end
          }
        }
      };
      await updateSettings(updatedSettings);
    }
  };

  const saveSessionTimeout = async () => {
    // Validate session timeout
    if (tempSessionTimeout < 5 || tempSessionTimeout > 480) {
      Alert.alert('Invalid Timeout', 'Session timeout must be between 5 and 480 minutes.');
      return;
    }

    try {
      setLocalSettings(prev => ({ ...prev, sessionTimeout: tempSessionTimeout }));

      if (settings) {
        const updatedSettings = {
          ...settings,
          account: {
            ...settings.account,
            sessionTimeout: tempSessionTimeout
          }
        };
        await updateSettings(updatedSettings);
        await settingsSyncService.updateSettings(updatedSettings);
      }

      triggerHaptic.medium();
    } catch (error) {
      console.error('Failed to save session timeout:', error);
      Alert.alert('Save Failed', 'Unable to save session timeout. Please try again.');
    }
  };

  // Help and support handlers
  const handleContactSupport = () => {
    triggerHaptic.light();
    setContactSubject('');
    setContactMessage('');
    setShowContactModal(true);
  };

  const submitContactForm = async () => {
    if (!contactSubject.trim() || !contactMessage.trim()) {
      Alert.alert('Required Fields', 'Please fill in both subject and message.');
      return;
    }

    triggerHaptic.medium();

    try {
      // Create support ticket
      const supportData = {
        subject: contactSubject.trim(),
        message: contactMessage.trim(),
        userId: profile?.id,
        userEmail: profile?.email,
        timestamp: new Date().toISOString(),
        platform: Platform.OS,
        appVersion: '1.0.0',
      };

      // In production, send to support API
      const response = await fetch('/api/support/tickets', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(supportData),
      });

      if (response.ok) {
        Alert.alert(
          'Message Sent',
          'Your message has been sent to our support team. We\'ll get back to you within 24 hours.',
          [{ text: 'OK' }]
        );

        setShowContactModal(false);
        setContactSubject('');
        setContactMessage('');
      } else {
        throw new Error('Failed to send support message');
      }
    } catch (error) {
      console.error('Support form error:', error);
      Alert.alert('Send Failed', 'Unable to send your message. Please try again.');
    }
  };

  const handleFeedback = () => {
    triggerHaptic.light();
    setFeedbackText('');
    setShowFeedbackModal(true);
  };

  const submitFeedback = async () => {
    if (!feedbackText.trim()) {
      Alert.alert('Feedback Required', 'Please enter your feedback.');
      return;
    }

    triggerHaptic.medium();

    try {
      // Create feedback entry
      const feedbackData = {
        feedback: feedbackText.trim(),
        userId: profile?.id,
        timestamp: new Date().toISOString(),
        platform: Platform.OS,
        appVersion: '1.0.0',
      };

      // In production, send to feedback API
      const response = await fetch('/api/feedback', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(feedbackData),
      });

      if (response.ok) {
        Alert.alert(
          'Feedback Sent',
          'Thank you for your feedback! It helps us improve the app.',
          [{ text: 'OK' }]
        );

        setShowFeedbackModal(false);
        setFeedbackText('');
      } else {
        throw new Error('Failed to send feedback');
      }
    } catch (error) {
      console.error('Feedback error:', error);
      Alert.alert('Send Failed', 'Unable to send your feedback. Please try again.');
    }
  };

  const handleFAQ = () => {
    triggerHaptic.light();
    setShowFAQModal(true);
  };

  const openExternalLink = async (url: string) => {
    triggerHaptic.light();
    try {
      const supported = await Linking.canOpenURL(url);
      if (supported) {
        await Linking.openURL(url);
      } else {
        Alert.alert('Error', 'Unable to open this link.');
      }
    } catch (error) {
      Alert.alert('Error', 'Unable to open this link.');
    }
  };

  // Advanced settings handlers
  const handleExportSettings = () => {
    triggerHaptic.medium();
    handleDataExport();
  };

  const handleImportSettings = async () => {
    setIsImporting(true);
    triggerHaptic.medium();

    try {
      const result = await DocumentPicker.getDocumentAsync({
        type: 'application/json',
        copyToCacheDirectory: true,
      });

      if (result.type === 'success') {
        const fileContent = await FileSystem.readAsStringAsync(result.uri);
        const importedData = JSON.parse(fileContent);

        if (importedData.settings && importedData.version) {
          Alert.alert(
            'Import Settings',
            'This will replace your current settings. Are you sure?',
            [
              { text: 'Cancel', style: 'cancel' },
              {
                text: 'Import',
                onPress: async () => {
                  try {
                    await updateSettings(importedData.settings);
                    await settingsSyncService.updateSettings(importedData.settings);
                    Alert.alert('Import Successful', 'Your settings have been imported.');
                  } catch (error) {
                    Alert.alert('Import Failed', 'Invalid settings file format.');
                  }
                }
              }
            ]
          );
        } else {
          Alert.alert('Invalid File', 'The selected file is not a valid settings export.');
        }
      }
    } catch (error) {
      console.error('Failed to import settings:', error);
      Alert.alert('Import Failed', 'Unable to import settings file.');
    } finally {
      setIsImporting(false);
    }
  };

  // Subscription management handlers
  const handleSubscriptionPress = () => {
    triggerHaptic.light();
    setShowSubscriptionModal(true);
  };

  const handleUpgrade = () => {
    triggerHaptic.medium();
    router.push('/premium');
  };

  const handleManageSubscription = () => {
    triggerHaptic.light();

    if (Platform.OS === 'ios') {
      Linking.openURL('https://apps.apple.com/account/subscriptions');
    } else if (Platform.OS === 'android') {
      Linking.openURL('https://play.google.com/store/account/subscriptions');
    } else {
      Alert.alert(
        'Manage Subscription',
        'Please visit your app store to manage your subscription.',
        [{ text: 'OK' }]
      );
    }
  };

  const handleCancelSubscription = async () => {
    triggerHaptic.heavy();

    Alert.alert(
      'Cancel Subscription',
      'Are you sure you want to cancel your premium subscription? You\'ll lose access to premium features at the end of your billing period.',
      [
        { text: 'Keep Subscription', style: 'cancel' },
        {
          text: 'Cancel Subscription',
          style: 'destructive',
          onPress: async () => {
            try {
              // Call subscription cancellation API
              const response = await fetch('/api/subscriptions/cancel', {
                method: 'POST',
                headers: {
                  'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                  userId: profile?.id,
                  subscriptionId: subscription?.id,
                }),
              });

              if (response.ok) {
                // Update premium store
                const premiumStore = usePremiumStore.getState();
                premiumStore.cancelSubscription();

                Alert.alert(
                  'Subscription Cancelled',
                  'Your subscription has been cancelled. You\'ll continue to have premium access until the end of your billing period.',
                  [{ text: 'OK' }]
                );
              } else {
                throw new Error('Failed to cancel subscription');
              }
            } catch (error) {
              console.error('Subscription cancellation error:', error);
              Alert.alert(
                'Cancellation Failed',
                'Unable to cancel your subscription. Please try again or contact support.',
                [{ text: 'OK' }]
              );
            }
          }
        }
      ]
    );
  };

  const handleResetSettings = () => {
    triggerHaptic.heavy();

    Alert.alert(
      'Reset Settings',
      'Are you sure you want to reset all settings to their default values?',
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'Reset', style: 'destructive', onPress: async () => {
          try {
            // Get default settings from service
            const defaultSettings = settingsSyncService.getDefaultSettings();
            await updateSettings(defaultSettings);
            await settingsSyncService.updateSettings(defaultSettings);

            // Update local state
            setLocalSettings({
              ageMin: defaultSettings.dating.ageRange.min,
              ageMax: defaultSettings.dating.ageRange.max,
              maxDistance: defaultSettings.dating.maxDistance,
              genderPreference: defaultSettings.dating.genderPreference,
              profileVisibility: defaultSettings.privacy.profileVisibility,
              showDistance: defaultSettings.privacy.showDistance,
              showAge: defaultSettings.privacy.showAge,
              showLastSeen: defaultSettings.privacy.showLastSeen,
              allowMessagesFrom: defaultSettings.privacy.allowMessagesFrom,
              showOnlineStatus: defaultSettings.privacy.showOnlineStatus,
              incognitoMode: defaultSettings.privacy.incognitoMode,
              pushNotifications: defaultSettings.notifications.pushNotifications,
              emailNotifications: defaultSettings.notifications.emailNotifications,
              newMatches: defaultSettings.notifications.newMatches,
              newMessages: defaultSettings.notifications.newMessages,
              likes: defaultSettings.notifications.likes,
              superLikes: defaultSettings.notifications.superLikes,
              promotions: defaultSettings.notifications.promotions,
              tips: defaultSettings.notifications.tips,
              soundEnabled: defaultSettings.notifications.soundEnabled,
              vibrationEnabled: defaultSettings.notifications.vibrationEnabled,
              quietHoursEnabled: defaultSettings.notifications.quietHours.enabled,
              quietHoursStart: defaultSettings.notifications.quietHours.startTime,
              quietHoursEnd: defaultSettings.notifications.quietHours.endTime,
              twoFactorAuth: defaultSettings.account.twoFactorAuth,
              loginAlerts: defaultSettings.account.loginAlerts,
              dataSharing: defaultSettings.account.dataSharing,
              analytics: defaultSettings.account.analytics,
              locationServices: defaultSettings.account.locationServices,
              autoRenewal: defaultSettings.account.autoRenewal,
              sessionTimeout: defaultSettings.account.sessionTimeout,
            });

            Alert.alert('Settings Reset', 'All settings have been reset to default values.');
          } catch (error) {
            Alert.alert('Reset Failed', 'Unable to reset settings. Please try again.');
          }
        }},
      ]
    );
  };

  // Navigation handlers with proper validation and error handling
  const handleEditProfile = () => {
    try {
      ProfileNavigationService.navigateToEdit();
    } catch (error) {
      console.error('Navigation to edit profile failed:', error);
      Alert.alert('Navigation Error', 'Unable to open edit profile. Please try again.');
    }
  };

  const handleManagePhotos = () => {
    try {
      ProfileNavigationService.navigateToPhotos();
    } catch (error) {
      console.error('Navigation to photos failed:', error);
      Alert.alert('Navigation Error', 'Unable to open photo management. Please try again.');
    }
  };

  const handleProfileAnalytics = () => {
    try {
      ProfileNavigationService.navigateToAnalytics();
    } catch (error) {
      console.error('Navigation to analytics failed:', error);
      Alert.alert('Navigation Error', 'Unable to open analytics. Please try again.');
    }
  };

  const handleNotifications = () => {
    try {
      ProfileNavigationService.navigateToNotifications();
    } catch (error) {
      console.error('Navigation to notifications failed:', error);
      Alert.alert('Navigation Error', 'Unable to open notifications. Please try again.');
    }
  };

  const handleProfileVerification = () => {
    try {
      ProfileNavigationService.navigateToVerification();
    } catch (error) {
      console.error('Navigation to verification failed:', error);
      Alert.alert('Navigation Error', 'Unable to open verification. Please try again.');
    }
  };

  const handlePremiumFeatures = () => {
    try {
      ProfileNavigationService.navigateToPremium();
    } catch (error) {
      console.error('Navigation to premium failed:', error);
      Alert.alert('Navigation Error', 'Unable to open premium features. Please try again.');
    }
  };

  const handleHelpSupport = () => {
    try {
      ProfileNavigationService.navigateToHelp();
    } catch (error) {
      console.error('Navigation to help failed:', error);
      Alert.alert('Navigation Error', 'Unable to open help & support. Please try again.');
    }
  };

  const handleAboutLegal = () => {
    try {
      ProfileNavigationService.navigateToAbout();
    } catch (error) {
      console.error('Navigation to about failed:', error);
      Alert.alert('Navigation Error', 'Unable to open about & legal. Please try again.');
    }
  };

  // Logout handler
  const handleLogout = () => {
    triggerHaptic.heavy();

    Alert.alert(
      'Log Out',
      'Are you sure you want to log out of your account?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Log Out',
          style: 'destructive',
          onPress: async () => {
            try {
              // Clear all local data
              await settingsSyncService.clearAllData();

              // Clear profile store
              const profileStore = useProfileStore.getState();
              profileStore.clearProfile();

              // Clear premium store
              const premiumStore = usePremiumStore.getState();
              premiumStore.clearSubscription();

              // Navigate to auth screen
              router.replace('/auth/login');
            } catch (error) {
              console.error('Logout error:', error);
              Alert.alert('Logout Failed', 'Unable to log out. Please try again.');
            }
          }
        }
      ]
    );
  };

  // Search and filter functions
  const handleSearch = (query: string) => {
    setSearchQuery(query.toLowerCase());
  };

  const handleFilter = (categories: string[]) => {
    setSelectedCategories(categories);
  };

  const shouldShowSection = (sectionName: string, items: string[]) => {
    // If no search query and no filters, show all sections
    if (!searchQuery && selectedCategories.length === 0) {
      return true;
    }

    // If categories are filtered and this section is not selected, hide it
    if (selectedCategories.length > 0 && !selectedCategories.includes(sectionName)) {
      return false;
    }

    // If there's a search query, check if any items in this section match
    if (searchQuery) {
      return items.some(item => item.toLowerCase().includes(searchQuery));
    }

    return true;
  };

  if (isLoading) {
    return (
      <LinearGradient colors={['#8B5CF6', '#EC4899']} style={styles.container}>
        <SafeAreaView style={styles.safeArea}>
          <LoadingSkeleton variant="settings" />
        </SafeAreaView>
      </LinearGradient>
    );
  }

  return (
    <LinearGradient colors={['#8B5CF6', '#EC4899']} style={styles.container}>
      <SafeAreaView style={styles.safeArea}>
        {/* Header */}
        <View style={styles.header}>
          <TouchableOpacity style={styles.backButton} onPress={() => router.back()}>
            <ArrowLeft size={24} color="white" />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Settings & Preferences</Text>
          <TouchableOpacity
            style={styles.advancedButton}
            onPress={() => setShowAdvancedSettings(!showAdvancedSettings)}
          >
            <Settings size={24} color="white" />
          </TouchableOpacity>
        </View>

        {/* Search and Filter */}
        <View style={styles.searchContainer}>
          <TextInput
            style={styles.searchInput}
            placeholder="Search settings..."
            value={searchQuery}
            onChangeText={handleSearch}
            placeholderTextColor={theme.colors.gray400}
          />
        </View>

        {/* Sync Manager */}
        <View style={styles.syncContainer}>
          <View style={styles.syncStatus}>
            <Text style={styles.syncStatusText}>Settings synchronized</Text>
          </View>
        </View>

        <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
          {isLoading ? (
            <LoadingSkeleton />
          ) : (
            <>
              {/* Profile Management */}
              <View style={styles.section}>
                <Text style={styles.sectionTitle}>Profile Management</Text>

                <SettingItem
                  icon={Edit3}
                  title="Edit Profile"
                  subtitle="Update your profile information"
                  type="button"
                  onPress={handleEditProfile}
                />

                <SettingItem
                  icon={Camera}
                  title="Manage Photos"
                  subtitle="Add, remove, or reorder your photos"
                  type="button"
                  onPress={handleManagePhotos}
                />

                <SettingItem
                  icon={BarChart3}
                  title="Profile Analytics"
                  subtitle="View your profile performance"
                  type="button"
                  onPress={handleProfileAnalytics}
                />

                <SettingItem
                  icon={Shield}
                  title="Profile Verification"
                  subtitle="Verify your profile for more matches"
                  type="button"
                  onPress={handleProfileVerification}
                />
              </View>

        {/* Dating Preferences */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Dating Preferences</Text>
          
          <SettingItem
            icon={Heart}
            title="Age Range"
            subtitle={`${localSettings.ageMin} - ${localSettings.ageMax} years`}
            type="button"
            onPress={handleAgeRangePress}
          />

          <SettingItem
            icon={MapPin}
            title="Maximum Distance"
            subtitle={`${localSettings.maxDistance} km`}
            type="button"
            onPress={handleDistancePress}
          />

          <SettingItem
            icon={User}
            title="Show Me"
            subtitle={localSettings.genderPreference === 'everyone' ? 'Everyone' : localSettings.genderPreference}
            type="button"
            onPress={handleGenderPress}
          />
        </View>

        {/* Privacy Settings */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Privacy & Safety</Text>
          
          <SettingItem
            icon={Eye}
            title="Show Distance"
            subtitle="Let others see how far away you are"
            value={localSettings.showDistance}
            onToggle={(value) => handleToggle('showDistance', value)}
          />
          
          <SettingItem
            icon={User}
            title="Show Age"
            subtitle="Display your age on your profile"
            value={localSettings.showAge}
            onToggle={(value) => handleToggle('showAge', value)}
          />
          
          <SettingItem
            icon={Eye}
            title="Show Last Seen"
            subtitle="Let others see when you were last active"
            value={localSettings.showLastSeen}
            onToggle={(value) => handleToggle('showLastSeen', value)}
          />
          
          <SettingItem
            icon={MessageCircle}
            title="Allow Messages From"
            subtitle={localSettings.allowMessagesFrom === 'everyone' ? 'Everyone' : 'Matches Only'}
            type="button"
            onPress={handleMessageSettingsPress}
          />
          
          <SettingItem
            icon={Shield}
            title="Incognito Mode"
            subtitle="Browse profiles privately"
            value={localSettings.incognitoMode}
            onToggle={(value) => handleToggle('incognitoMode', value)}
          />
        </View>

        {/* Notification Settings */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Notifications</Text>
          
          <SettingItem
            icon={Bell}
            title="Push Notifications"
            subtitle="Receive notifications on your device"
            value={localSettings.pushNotifications}
            onToggle={(value) => handleNotificationToggle('pushNotifications', value)}
          />

          {localSettings.pushNotifications && (
            <SettingItem
              icon={TestTube}
              title="Test Notifications"
              subtitle="Send a test notification"
              type="button"
              onPress={testNotification}
            />
          )}
          
          <SettingItem
            icon={Heart}
            title="New Matches"
            subtitle="Get notified when you have a new match"
            value={localSettings.newMatches}
            onToggle={(value) => handleToggle('newMatches', value)}
          />
          
          <SettingItem
            icon={MessageCircle}
            title="New Messages"
            subtitle="Get notified when you receive messages"
            value={localSettings.newMessages}
            onToggle={(value) => handleToggle('newMessages', value)}
          />
          
          <SettingItem
            icon={Volume2}
            title="Sound"
            subtitle="Play sounds for notifications"
            value={localSettings.soundEnabled}
            onToggle={(value) => handleToggle('soundEnabled', value)}
          />
          
          <SettingItem
            icon={Vibrate}
            title="Vibration"
            subtitle="Vibrate for notifications"
            value={localSettings.vibrationEnabled}
            onToggle={(value) => handleToggle('vibrationEnabled', value)}
          />
          
          <SettingItem
            icon={Moon}
            title="Quiet Hours"
            subtitle={localSettings.quietHoursEnabled ? `${localSettings.quietHoursStart} - ${localSettings.quietHoursEnd}` : 'Disabled'}
            value={localSettings.quietHoursEnabled}
            onToggle={(value) => handleToggle('quietHoursEnabled', value)}
          />

          {localSettings.quietHoursEnabled && (
            <SettingItem
              icon={Clock}
              title="Set Quiet Hours"
              subtitle="Configure when to pause notifications"
              type="button"
              onPress={handleQuietHoursPress}
            />
          )}
        </View>

        {/* Account Settings */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Account & Security</Text>
          
          <SettingItem
            icon={Shield}
            title="Two-Factor Authentication"
            subtitle="Add an extra layer of security"
            value={localSettings.twoFactorAuth}
            onToggle={(value) => handleToggle('twoFactorAuth', value)}
          />
          
          <SettingItem
            icon={Bell}
            title="Login Alerts"
            subtitle="Get notified of new logins"
            value={localSettings.loginAlerts}
            onToggle={(value) => handleToggle('loginAlerts', value)}
          />
          
          <SettingItem
            icon={MapPin}
            title="Location Services"
            subtitle="Allow location access for better matches"
            value={localSettings.locationServices}
            onToggle={(value) => handleToggle('locationServices', value)}
          />

          <SettingItem
            icon={Clock}
            title="Session Timeout"
            subtitle={`Auto-logout after ${localSettings.sessionTimeout || 30} minutes`}
            type="button"
            onPress={handleSessionTimeoutPress}
          />
        </View>

        {/* App Preferences */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>App Preferences</Text>

          <SettingItem
            icon={Settings}
            title="Language"
            subtitle={languages.find(l => l.code === (settings?.app.language || 'en'))?.name || 'English'}
            type="button"
            onPress={handleLanguagePress}
          />

          <SettingItem
            icon={Moon}
            title="Theme"
            subtitle={themes.find(t => t.value === (settings?.app.theme || 'system'))?.name || 'System'}
            type="button"
            onPress={handleThemePress}
          />

          <SettingItem
            icon={MapPin}
            title="Units"
            subtitle={units.find(u => u.value === (settings?.app.units || 'metric'))?.name || 'Metric'}
            type="button"
            onPress={handleUnitsPress}
          />


        </View>

        {/* Subscription Management */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Subscription</Text>

          {isPremium ? (
            <>
              <SettingItem
                icon={Award}
                title="Premium Active"
                subtitle={subscription ? `Expires ${new Date(subscription.expiresAt).toLocaleDateString()}` : 'Premium features enabled'}
                type="button"
                onPress={handleSubscriptionPress}
              />

              <SettingItem
                icon={Settings}
                title="Manage Subscription"
                subtitle="Change plan or billing details"
                type="button"
                onPress={handleManageSubscription}
              />

              <SettingItem
                icon={X}
                title="Cancel Subscription"
                subtitle="Cancel your premium subscription"
                type="button"
                onPress={handleCancelSubscription}
              />
            </>
          ) : (
            <SettingItem
              icon={Award}
              title="Upgrade to Premium"
              subtitle="Unlock exclusive features and benefits"
              type="button"
              onPress={handleUpgrade}
            />
          )}
        </View>

        {/* Help & Support */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Help & Support</Text>

          <SettingItem
            icon={Info}
            title="FAQ"
            subtitle="Frequently asked questions"
            type="button"
            onPress={handleFAQ}
          />

          <SettingItem
            icon={MessageCircle}
            title="Contact Support"
            subtitle="Get help from our support team"
            type="button"
            onPress={handleContactSupport}
          />

          <SettingItem
            icon={Heart}
            title="Send Feedback"
            subtitle="Help us improve the app"
            type="button"
            onPress={handleFeedback}
          />

          <SettingItem
            icon={Info}
            title="Privacy Policy"
            subtitle="Read our privacy policy"
            type="button"
            onPress={() => openExternalLink('https://datingapp.com/privacy-policy')}
          />

          <SettingItem
            icon={Info}
            title="Terms of Service"
            subtitle="Read our terms of service"
            type="button"
            onPress={() => openExternalLink('https://datingapp.com/terms-of-service')}
          />
        </View>

        {/* Quick Actions */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Quick Actions</Text>

          <SettingItem
            icon={Bell}
            title="Notifications"
            subtitle="Manage notification preferences"
            type="button"
            onPress={handleNotifications}
          />

          <SettingItem
            icon={Award}
            title="Premium Features"
            subtitle="Explore premium benefits"
            type="button"
            onPress={handlePremiumFeatures}
          />

          <SettingItem
            icon={HelpCircle}
            title="Help & Support"
            subtitle="Get help and contact support"
            type="button"
            onPress={handleHelpSupport}
          />

          <SettingItem
            icon={Info}
            title="About & Legal"
            subtitle="App information and legal documents"
            type="button"
            onPress={handleAboutLegal}
          />
        </View>

        {/* Advanced Settings */}
        {showAdvancedSettings && (
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Advanced Settings</Text>

            <SettingItem
              icon={Download}
              title="Export Settings"
              subtitle="Save your settings to a file"
              type="button"
              onPress={handleExportSettings}
            />

            <SettingItem
              icon={Upload}
              title="Import Settings"
              subtitle="Load settings from a file"
              type="button"
              onPress={handleImportSettings}
            />

            <SettingItem
              icon={RotateCcw}
              title="Reset to Defaults"
              subtitle="Reset all settings to default values"
              type="button"
              onPress={handleResetSettings}
            />

            {/* Development/Testing Menu Items */}
            {__DEV__ && (
              <>
                <SettingItem
                  icon={Settings}
                  title="Settings Test Suite"
                  subtitle="Test settings functionality"
                  type="button"
                  onPress={() => router.push('/test/settings-suite')}
                />

                <SettingItem
                  icon={TestTube}
                  title="Web Compatibility Test"
                  subtitle="Test web browser compatibility"
                  type="button"
                  onPress={() => Alert.alert('Web Test', 'Web compatibility test completed successfully!')}
                />
              </>
            )}
          </View>
        )}

        {/* Danger Zone */}
        <View style={styles.section}>
          <Text style={[styles.sectionTitle, styles.dangerTitle]}>Danger Zone</Text>

          <TouchableOpacity style={styles.dangerItem} onPress={handleLogout}>
            <View style={styles.settingLeft}>
              <View style={[styles.settingIcon, styles.dangerIcon]}>
                <ArrowLeft size={20} color={theme.colors.warning} />
              </View>
              <View style={styles.settingContent}>
                <Text style={[styles.settingTitle, styles.dangerText]}>Log Out</Text>
                <Text style={styles.settingSubtitle}>Sign out of your account</Text>
              </View>
            </View>
          </TouchableOpacity>

          <TouchableOpacity style={styles.dangerItem} onPress={handleDataExport}>
            <View style={styles.settingLeft}>
              <View style={[styles.settingIcon, styles.dangerIcon]}>
                <Download size={20} color={theme.colors.warning} />
              </View>
              <View style={styles.settingContent}>
                <Text style={[styles.settingTitle, { color: theme.colors.warning }]}>Export Data</Text>
                <Text style={styles.settingSubtitle}>Download a copy of your data</Text>
              </View>
            </View>
          </TouchableOpacity>

          <TouchableOpacity style={styles.dangerItem} onPress={handleDeleteAccount}>
            <View style={styles.settingLeft}>
              <View style={[styles.settingIcon, styles.dangerIcon]}>
                <Trash2 size={20} color={theme.colors.error} />
              </View>
              <View style={styles.settingContent}>
                <Text style={[styles.settingTitle, styles.dangerText]}>Delete Account</Text>
                <Text style={styles.settingSubtitle}>Permanently delete your account and data</Text>
              </View>
            </View>
          </TouchableOpacity>
        </View>
        </>
        )}
      </ScrollView>

      {/* Modals */}
      <SettingsModal
        visible={showAgeRangeModal}
        onClose={() => setShowAgeRangeModal(false)}
        title="Age Range"
        onSave={saveAgeRange}
      >
        <RangePicker
          min={18}
          max={80}
          step={1}
          initialMinValue={tempAgeRange.min}
          initialMaxValue={tempAgeRange.max}
          onValueChange={(min, max) => setTempAgeRange({ min, max })}
          formatLabel={(value) => `${value} years`}
          title="Select Age Range"
        />
      </SettingsModal>

      <SettingsModal
        visible={showDistanceModal}
        onClose={() => setShowDistanceModal(false)}
        title="Maximum Distance"
        onSave={saveDistance}
      >
        <DistancePicker
          initialDistance={tempDistance}
          onDistanceChange={setTempDistance}
          units="km"
          showUnitsToggle={true}
          title="Select Maximum Distance"
        />
      </SettingsModal>

      <SettingsModal
        visible={showGenderModal}
        onClose={() => setShowGenderModal(false)}
        title="Show Me"
        onSave={saveGender}
      >
        <View style={styles.genderOptions}>
          {['everyone', 'men', 'women', 'non-binary'].map((option) => (
            <TouchableOpacity
              key={option}
              style={[
                styles.genderOption,
                tempGender === option && styles.genderOptionSelected,
              ]}
              onPress={() => setTempGender(option)}
            >
              <Text
                style={[
                  styles.genderOptionText,
                  tempGender === option && styles.genderOptionTextSelected,
                ]}
              >
                {option === 'everyone' ? 'Everyone' : option.charAt(0).toUpperCase() + option.slice(1)}
              </Text>
              {tempGender === option && <Check size={20} color="white" />}
            </TouchableOpacity>
          ))}
        </View>
      </SettingsModal>

      <SettingsModal
        visible={showQuietHoursModal}
        onClose={() => setShowQuietHoursModal(false)}
        title="Quiet Hours"
        onSave={saveQuietHours}
      >
        <View style={styles.quietHoursContainer}>
          <View style={styles.timePickerContainer}>
            <Text style={styles.timePickerLabel}>Start Time</Text>
            <TimePicker
              initialTime={tempQuietHours.start}
              onTimeChange={(time) => setTempQuietHours(prev => ({ ...prev, start: time }))}
              title="Start Time"
            />
          </View>
          <View style={styles.timePickerContainer}>
            <Text style={styles.timePickerLabel}>End Time</Text>
            <TimePicker
              initialTime={tempQuietHours.end}
              onTimeChange={(time) => setTempQuietHours(prev => ({ ...prev, end: time }))}
              title="End Time"
            />
          </View>
        </View>
      </SettingsModal>

      <SettingsModal
        visible={showSessionTimeoutModal}
        onClose={() => setShowSessionTimeoutModal(false)}
        title="Session Timeout"
        onSave={saveSessionTimeout}
      >
        <SessionTimeoutPicker
          initialTimeout={tempSessionTimeout}
          onTimeoutChange={setTempSessionTimeout}
          title="Auto-logout after inactivity"
        />
      </SettingsModal>

      {/* Language Selection Modal */}
      <SettingsModal
        visible={showLanguageModal}
        onClose={() => setShowLanguageModal(false)}
        title="Language"
        onSave={saveLanguage}
      >
        <View style={styles.genderOptions}>
          {languages.map((language) => (
            <TouchableOpacity
              key={language.code}
              style={[
                styles.genderOption,
                tempLanguage === language.code && styles.genderOptionSelected,
              ]}
              onPress={() => setTempLanguage(language.code)}
            >
              <Text style={styles.genderOptionText}>
                {language.flag} {language.name}
              </Text>
              {tempLanguage === language.code && <Check size={20} color="white" />}
            </TouchableOpacity>
          ))}
        </View>
      </SettingsModal>

      {/* Theme Selection Modal */}
      <SettingsModal
        visible={showThemeModal}
        onClose={() => setShowThemeModal(false)}
        title="Theme"
        onSave={saveTheme}
      >
        <View style={styles.genderOptions}>
          {themes.map((theme) => (
            <TouchableOpacity
              key={theme.value}
              style={[
                styles.genderOption,
                tempTheme === theme.value && styles.genderOptionSelected,
              ]}
              onPress={() => setTempTheme(theme.value)}
            >
              <View style={styles.settingContent}>
                <Text style={[
                  styles.genderOptionText,
                  tempTheme === theme.value && styles.genderOptionTextSelected,
                ]}>
                  {theme.name}
                </Text>
                <Text style={[
                  styles.settingSubtitle,
                  tempTheme === theme.value && { color: 'rgba(255,255,255,0.8)' },
                ]}>
                  {theme.description}
                </Text>
              </View>
              {tempTheme === theme.value && <Check size={20} color="white" />}
            </TouchableOpacity>
          ))}
        </View>
      </SettingsModal>

      {/* Units Selection Modal */}
      <SettingsModal
        visible={showUnitsModal}
        onClose={() => setShowUnitsModal(false)}
        title="Units"
        onSave={saveUnits}
      >
        <View style={styles.genderOptions}>
          {units.map((unit) => (
            <TouchableOpacity
              key={unit.value}
              style={[
                styles.genderOption,
                tempUnits === unit.value && styles.genderOptionSelected,
              ]}
              onPress={() => setTempUnits(unit.value)}
            >
              <View style={styles.settingContent}>
                <Text style={[
                  styles.genderOptionText,
                  tempUnits === unit.value && styles.genderOptionTextSelected,
                ]}>
                  {unit.name}
                </Text>
                <Text style={[
                  styles.settingSubtitle,
                  tempUnits === unit.value && { color: 'rgba(255,255,255,0.8)' },
                ]}>
                  {unit.description}
                </Text>
              </View>
              {tempUnits === unit.value && <Check size={20} color="white" />}
            </TouchableOpacity>
          ))}
        </View>
      </SettingsModal>

      {/* Message Settings Modal */}
      <SettingsModal
        visible={showMessageSettingsModal}
        onClose={() => setShowMessageSettingsModal(false)}
        title="Allow Messages From"
        onSave={saveMessageSettings}
      >
        <View style={styles.genderOptions}>
          {[
            { value: 'everyone', name: 'Everyone', description: 'Anyone can message you' },
            { value: 'matches', name: 'Matches Only', description: 'Only your matches can message you' },
            { value: 'premium', name: 'Premium Users', description: 'Only premium users can message you' },
          ].map((option) => (
            <TouchableOpacity
              key={option.value}
              style={[
                styles.genderOption,
                tempMessageSettings === option.value && styles.genderOptionSelected,
              ]}
              onPress={() => setTempMessageSettings(option.value)}
            >
              <View style={styles.settingContent}>
                <Text style={[
                  styles.genderOptionText,
                  tempMessageSettings === option.value && styles.genderOptionTextSelected,
                ]}>
                  {option.name}
                </Text>
                <Text style={[
                  styles.settingSubtitle,
                  tempMessageSettings === option.value && { color: 'rgba(255,255,255,0.8)' },
                ]}>
                  {option.description}
                </Text>
              </View>
              {tempMessageSettings === option.value && <Check size={20} color="white" />}
            </TouchableOpacity>
          ))}
        </View>
      </SettingsModal>

      {/* FAQ Modal */}
      <SettingsModal
        visible={showFAQModal}
        onClose={() => setShowFAQModal(false)}
        title="Frequently Asked Questions"
        showSaveButton={false}
      >
        <ScrollView style={styles.faqContainer}>
          {faqItems.map((item, index) => (
            <View key={index} style={styles.faqItem}>
              <Text style={styles.faqQuestion}>{item.question}</Text>
              <Text style={styles.faqAnswer}>{item.answer}</Text>
            </View>
          ))}
        </ScrollView>
      </SettingsModal>

      {/* Contact Support Modal */}
      <SettingsModal
        visible={showContactModal}
        onClose={() => setShowContactModal(false)}
        title="Contact Support"
        onSave={submitContactForm}
        saveButtonText="Send Message"
      >
        <View style={styles.contactForm}>
          <Text style={styles.formLabel}>Subject</Text>
          <TextInput
            style={styles.formInput}
            placeholder="What can we help you with?"
            value={contactSubject}
            onChangeText={setContactSubject}
            placeholderTextColor={theme.colors.gray400}
          />

          <Text style={styles.formLabel}>Message</Text>
          <TextInput
            style={[styles.formInput, styles.formTextArea]}
            placeholder="Describe your issue or question..."
            value={contactMessage}
            onChangeText={setContactMessage}
            multiline
            numberOfLines={6}
            textAlignVertical="top"
            placeholderTextColor={theme.colors.gray400}
          />
        </View>
      </SettingsModal>

      {/* Feedback Modal */}
      <SettingsModal
        visible={showFeedbackModal}
        onClose={() => setShowFeedbackModal(false)}
        title="Send Feedback"
        onSave={submitFeedback}
        saveButtonText="Send Feedback"
      >
        <View style={styles.feedbackForm}>
          <Text style={styles.formLabel}>Your Feedback</Text>
          <TextInput
            style={[styles.formInput, styles.formTextArea]}
            placeholder="Tell us what you think about the app..."
            value={feedbackText}
            onChangeText={setFeedbackText}
            multiline
            numberOfLines={8}
            textAlignVertical="top"
            placeholderTextColor={theme.colors.gray400}
          />
        </View>
      </SettingsModal>

      {/* Delete Account Modal */}
      <SettingsModal
        visible={showDeleteAccountModal}
        onClose={() => setShowDeleteAccountModal(false)}
        title="Delete Account"
        onSave={confirmDeleteAccount}
        saveButtonText={isDeletingAccount ? "Deleting..." : "Delete Account"}
      >
        <View style={styles.deleteAccountForm}>
          <Text style={styles.deleteWarning}>
            ⚠️ This action cannot be undone. All your data will be permanently deleted.
          </Text>

          <Text style={styles.deleteInstructions}>
            To confirm deletion, type "delete" below:
          </Text>

          <TextInput
            style={styles.deleteConfirmInput}
            placeholder="Type 'delete' to confirm"
            value={deleteConfirmText}
            onChangeText={setDeleteConfirmText}
            placeholderTextColor={theme.colors.gray400}
            autoCapitalize="none"
          />

          {isDeletingAccount && (
            <View style={styles.deletingIndicator}>
              <Text style={styles.deletingText}>Deleting your account...</Text>
            </View>
          )}
        </View>
      </SettingsModal>

      {/* Subscription Details Modal */}
      <SettingsModal
        visible={showSubscriptionModal}
        onClose={() => setShowSubscriptionModal(false)}
        title="Subscription Details"
        showSaveButton={false}
      >
        <View style={styles.subscriptionDetails}>
          {subscription ? (
            <>
              <Text style={styles.subscriptionPlan}>
                {subscription.planName || 'Premium Plan'}
              </Text>
              <Text style={styles.subscriptionStatus}>
                Status: {subscription.status}
              </Text>
              <Text style={styles.subscriptionExpiry}>
                Expires: {new Date(subscription.expiresAt).toLocaleDateString()}
              </Text>
              {subscription.autoRenew && (
                <Text style={styles.subscriptionRenewal}>
                  Auto-renewal enabled
                </Text>
              )}
            </>
          ) : (
            <Text style={styles.noSubscription}>
              No active subscription
            </Text>
          )}
        </View>
      </SettingsModal>

      {/* Web Compatibility Test Modal */}
      {/* <SettingsModal
        visible={showWebTest}
        onClose={() => setShowWebTest(false)}
        title="Web Compatibility Test"
        showSaveButton={false}
      >
        <WebCompatibilityTest />
      </SettingsModal> */}

      </SafeAreaView>
    </LinearGradient>
  );
}
